import { onBeforeUnmount, onMounted, ref, watch } from "vue";
import * as d3 from "d3";

// 预加载所有图标
const iconModules = import.meta.glob("@/assets/images/*_icon.png", { eager: true });

/**
 * D3拓扑图核心逻辑Hook
 * @param {Object} props - 组件属性
 * @param {Function} emit - 事件发射器
 * @param {Object} options - 额外选项
 * @returns {Object} - 返回拓扑图相关的状态和方法
 */
export function useD3Topology(props, emit, options = {}) {
  const { t, locale, globalStore } = options;

  // 容器引用
  const container = ref(null);
  let svg = null;

  // 布局方向
  const currentDirection = ref(props.direction);

  // 存储当前的变换状态
  const currentTransform = ref(null);

  // 存储缩放行为和SVG引用
  const zoomBehaviorRef = ref(null);

  // 设备信息弹出相关状态
  const selectedDevice = ref({});
  const showDeviceInfo = ref(false);
  const popupX = ref(0);
  const popupY = ref(0);

  // 刷新拓扑图
  const refreshTopology = () => {
    emit("refresh");
    initChart();
  };

  // 切换布局方向
  const toggleDirection = () => {
    currentDirection.value = currentDirection.value === "vertical" ? "horizontal" : "vertical";
    initChart();
  };

  // 恢复原始大小
  const resetZoom = () => {
    if (zoomBehaviorRef.value) {
      const { zoomBehavior, svg } = zoomBehaviorRef.value;

      // 创建一个新的变换，包含初始的居中偏移
      const initialTransform = d3.zoomIdentity.translate(
        props.width / 2, // 水平方向居中
        currentDirection.value === "vertical" ? props.height / 3 : props.height / 2 // 在纵向布局下将树的顶部移到更高的位置
      );

      // 应用这个变换
      svg.transition().duration(750).call(zoomBehavior.transform, initialTransform);
    }
  };

  // 关闭设备信息弹出
  const closeDeviceInfo = () => {
    showDeviceInfo.value = false;
  };

  // 点击容器外部时关闭设备信息
  const handleDocumentClick = event => {
    // 如果点击的不是节点元素，则关闭设备信息
    if (!event.target.closest(".node") && showDeviceInfo.value) {
      showDeviceInfo.value = false;
    }
  };

  // 转换数据格式
  const transformData = data => {
    if (!data) return { root: null, nodes: [], links: [] };

    const nodes = [];
    const links = [];
    const nodeMap = new Map();

    // 递归处理节点
    const processNode = (node, parentId = null, depth = 0) => {
      if (!node) return null;

      // 创建节点ID
      const nodeId = node.id || node.deviceId || `node-${nodes.length}`;

      // 创建新节点
      const newNode = {
        id: nodeId,
        name: node.name || "未命名节点",
        symbol: node.symbol || "",
        extra: node.extra || {},
        depth: depth,
        children: [],
        x0: 0,
        y0: 0,
        // 保留出口设备相关属性
        isExitDevice: !!node.isExitDevice,
        canParentBeExitDevice: node.canParentBeExitDevice
      };

      // 添加节点
      if (!nodeMap.has(nodeId)) {
        nodes.push(newNode);
        nodeMap.set(nodeId, newNode);
      }

      // 添加连线
      if (parentId) {
        const medium = node.extra?.medium || "CABLE";
        links.push({
          source: parentId,
          target: nodeId,
          medium: medium
        });
      }

      // 处理子节点
      if (node.children && Array.isArray(node.children)) {
        newNode.children = node.children
          .map(child => {
            return processNode(child, nodeId, depth + 1);
          })
          .filter(Boolean);
      }

      return newNode;
    };

    // 处理根节点
    const root = processNode(data);

    return { root, nodes, links };
  };

  // 统计树结构最大深度和每层最大节点数
  function getTreeStats(root) {
    let maxDepth = 0;
    let maxBreadth = 0;
    const levelMap = {};
    function traverse(node, depth = 0) {
      maxDepth = Math.max(maxDepth, depth);
      levelMap[depth] = (levelMap[depth] || 0) + 1;
      maxBreadth = Math.max(maxBreadth, levelMap[depth]);
      (node.children || []).forEach(child => traverse(child, depth + 1));
    }
    traverse(root);
    return { maxDepth, maxBreadth };
  }

  // 初始化图表
  const initChart = () => {
    console.log("Initializing chart...");
    if (!container.value || !props.data) {
      console.log("Container or data not available", container.value, props.data);
      return;
    }

    // 清除旧图表
    d3.select(container.value).selectAll("*").remove();
    console.log("Container cleared");

    // 确保宽度和高度始终为正值
    const width = Math.max(100, props.width || 100);
    const height = Math.max(100, props.height || 100);

    console.log(`创建SVG元素: 宽度=${width}, 高度=${height}`);

    // 处理数据
    const { root } = transformData(props.data);
    if (!root) return;

    // 统计最大层级和最大宽度
    const { maxDepth, maxBreadth } = getTreeStats(root);
    const padding = 40;
    const minIconSize = 24;
    const maxIconSize = 60;
    let iconSize;
    if (currentDirection.value === "vertical") {
      iconSize = Math.min(
        Math.max(minIconSize, (width - 2 * padding) / (maxBreadth + 1)),
        Math.max(minIconSize, (height - 2 * padding) / (maxDepth + 1)),
        maxIconSize
      );
    } else {
      iconSize = Math.min(
        Math.max(minIconSize, (height - 2 * padding) / (maxBreadth + 1)),
        Math.max(minIconSize, (width - 2 * padding) / (maxDepth + 1)),
        maxIconSize
      );
    }
    // 节点间距
    const nodeWidth = iconSize * 2.2;
    const nodeHeight = iconSize * 2.5;

    // 创建SVG
    svg = d3
      .select(container.value)
      .append("svg")
      .attr("width", width)
      .attr("height", height)
      .attr("viewBox", [0, 0, width, height])
      .attr("style", "max-width: 100%; height: auto;")
      .on("click", () => {
        // 点击空白区域关闭设备信息弹出
        showDeviceInfo.value = false;
      });

    // 创建主容器
    const g = svg.append("g");

    // 添加灰度滤镜定义，用于离线节点
    const defs = svg.append("defs");
    const grayscaleFilter = defs
      .append("filter")
      .attr("id", "grayscale")
      .attr("x", "0")
      .attr("y", "0")
      .attr("width", "100%")
      .attr("height", "100%");

    grayscaleFilter
      .append("feColorMatrix")
      .attr("type", "matrix")
      .attr("values", "0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0");

    // 添加缩放功能
    const zoomBehavior = d3
      .zoom()
      .scaleExtent([0.1, 4])
      .on("zoom", event => {
        g.attr("transform", event.transform);
        currentTransform.value = event.transform;
      })
      .filter(event => {
        return !event.target.closest(".node");
      });

    // 创建树形布局
    const treeLayout = d3.tree();
    if (currentDirection.value === "vertical") {
      treeLayout.size([width - 2 * padding, height - 2 * padding]);
      treeLayout.nodeSize([nodeWidth, nodeHeight]);
    } else {
      treeLayout.size([height - 2 * padding, width - 2 * padding]);
      treeLayout.nodeSize([nodeHeight, nodeWidth]);
    }
    const rootNode = d3.hierarchy(root);
    treeLayout(rootNode);

    // 计算所有节点的实际边界（为下方/右侧增加20px安全边距）
    const safeMargin = 70;
    const nodes = rootNode.descendants();
    const minX = d3.min(nodes, d => (currentDirection.value === "vertical" ? d.x - iconSize / 2 : d.y - iconSize / 2));
    const maxX =
      d3.max(nodes, d => (currentDirection.value === "vertical" ? d.x + iconSize / 2 : d.y + iconSize / 2)) + safeMargin;
    const minY = d3.min(nodes, d => (currentDirection.value === "vertical" ? d.y - iconSize / 2 : d.x - iconSize / 2));
    const maxY =
      d3.max(nodes, d => (currentDirection.value === "vertical" ? d.y + iconSize / 2 : d.x + iconSize / 2)) + safeMargin;
    const contentWidth = maxX - minX;
    const contentHeight = maxY - minY;
    // 计算缩放比例
    const scale = Math.min((width - 2 * padding) / contentWidth, (height - 2 * padding) / contentHeight, 1);
    // 计算偏移量（让内容居中）
    const offsetX = (width - contentWidth * scale) / 2 - minX * scale;
    const offsetY = (height - contentHeight * scale) / 2 - minY * scale;
    // 应用初始变换
    const initialTransform = d3.zoomIdentity.translate(offsetX, offsetY).scale(scale);
    svg.call(zoomBehavior).call(zoomBehavior.transform, initialTransform);
    zoomBehaviorRef.value = { zoomBehavior, svg };

    // 为节点添加点击事件处理函数
    const handleNodeClick = (event, d) => {
      event.stopPropagation();
      event.preventDefault(); // 阻止默认行为

      // 如果是internet节点，不显示设备信息弹窗
      if (d.data.name === "internet") {
        // 只触发节点点击事件，不显示弹窗
        emit("nodeClick", d.data);
        return;
      }

      // 获取节点在屏幕上的坐标
      const nodeElement = event.currentTarget;
      const rect = nodeElement.getBoundingClientRect();

      // 设置弹出位置 - 使用节点中心坐标，而不是鼠标坐标
      popupX.value = rect.left + rect.width / 2; // 节点中心的X坐标
      popupY.value = rect.top + rect.height / 2; // 节点中心的Y坐标

      // 设置选中设备信息
      const extra = d.data.extra || {};

      // 获取父节点信息
      const parentNode = d.parent ? d.parent.data : null;

      // 计算节点是否可以设置为出口设备
      let canParentBeExitDevice = false;

      // 检查节点是否已经是出口设备
      const isExitDevice = !!d.data.isExitDevice;

      // 二级节点（直接连接到Internet的节点）不能设置为出口设备
      // 但如果已经是出口设备，则可以取消
      if (d.depth === 1) {
        // 二级节点不能设置为出口设备，但如果已经是出口设备，则保留该状态
        canParentBeExitDevice = false;
      }
      // 从三级节点开始判断（d.depth >= 2，因为根节点depth=0，一级节点depth=1）
      else if (d.depth >= 2) {
        // 获取sport和dport
        const sport = extra.sport || "";
        const dport = extra.dport || "";

        // 转换为小写
        const sportLower = sport.toLowerCase();
        const dportLower = dport.toLowerCase();

        // 获取端口类型
        const sportType = getPortType(sportLower);
        const dportType = getPortType(dportLower);
        // 判断端口类型是否为空
        // 检查端口是否为空
        if (!sport || !dport) {
          console.log(`节点 ${node.name} 的端口信息不完整，sport=${sport}, dport=${dport}`);
          canParentBeExitDevice = false;
        } else {
          console.log(`节点 ${d.data.name} 的端口类型: sport=${sportLower}(${sportType}), dport=${dportLower}(${dportType})`);

          // 如果sport和dport属于同一类型，则该节点可以设置为出口设备
          canParentBeExitDevice = sportType === dportType;

          // 如果父节点不能设置为出口设备，则子节点也不能
          if (parentNode && parentNode.canParentBeExitDevice === false) {
            canParentBeExitDevice = false;
          }
        }
      }

      selectedDevice.value = {
        ...d.data,
        // 确保关键字段存在，优先使用extra中的字段
        name: extra.deviceName || d.data.name || "",
        deviceName: extra.deviceName || d.data.deviceName || "",
        deviceModel: extra.deviceModel || d.data.deviceModel || "",
        deviceType: extra.deviceType || d.data.deviceType || "",
        // 优先使用extra.deviceId作为设备ID
        deviceId: extra.deviceId || d.data.deviceId || "",
        // 添加SN序列号字段
        sn: extra.sn || d.data.sn || "",
        ipaddr: extra.ipaddr || d.data.ipaddr || "",
        mac: extra.mac || extra.macaddr || d.data.mac || d.data.macaddr || "",
        macaddr: extra.macaddr || d.data.macaddr || "",
        // 添加父节点信息和出口设备标志
        parentNode: parentNode,
        canParentBeExitDevice: canParentBeExitDevice,
        isExitDevice: isExitDevice,
        extra: extra
      };

      // 显示弹出框
      showDeviceInfo.value = true;

      // 触发节点点击事件
      emit("nodeClick", d.data);
    };

    // 创建连线
    g.append("g")
      .attr("fill", "none")
      .attr("stroke", () => {
        return "#409EFF";
      })
      .attr("stroke-opacity", 0.8)
      .selectAll("path")
      .data(rootNode.links())
      .join("path")
      .attr("stroke-width", 2)
      .attr("stroke", () => {
        return "#409EFF";
      })
      .attr("stroke-dasharray", d => {
        const medium = d.source.data.extra?.medium || d.target.data.extra?.medium || "CABLE";
        return medium === "RADIO" ? "5,5" : null;
      })
      .attr("d", d => {
        const iconRadius = iconSize / 2;
        if (currentDirection.value === "vertical") {
          const sx = d.source.x;
          const sy = d.source.y + iconRadius;
          const tx = d.target.x;
          const ty = d.target.y - iconRadius;
          const midY = (sy + ty) / 2;
          return `M${sx},${sy} L${sx},${midY} L${tx},${midY} L${tx},${ty}`;
        } else {
          const sx = d.source.y + iconRadius;
          const sy = d.source.x;
          const tx = d.target.y - iconRadius;
          const ty = d.target.x;
          const midX = (sx + tx) / 2;
          return `M${sx},${sy} L${midX},${sy} L${midX},${ty} L${tx},${ty}`;
        }
      });

    // 创建节点组
    const node = g
      .append("g")
      .selectAll(".node")
      .data(rootNode.descendants())
      .join("g")
      .attr("class", "node")
      .attr("transform", d => {
        return currentDirection.value === "vertical" ? `translate(${d.x},${d.y})` : `translate(${d.y},${d.x})`;
      })
      .style("cursor", "pointer")
      .on("click", handleNodeClick);

    // 添加节点图标
    node
      .append("image")
      .attr("xlink:href", d => {
        let url = d.data.symbol || "";
        if (!url) {
          console.warn("No symbol found for node:", d.data);
          return ""; // 返回空字符串避免错误请求
        }

        // 采用最简单的方法处理图标URL
        try {
          // 如果是image://前缀，先删除前缀
          if (url.startsWith("image://")) {
            const isGray = url.startsWith("image://gray:");
            url = isGray ? url.substring(12) : url.substring(7);
          }

          // 如果是相对路径，确保它是绝对路径
          if (!url.startsWith("http") && !url.startsWith("/") && !url.startsWith("data:")) {
            url = "/" + url;
          }

          // 提取文件名
          const filename = url.split("/").pop();

          // 根据文件名判断图标类型
          if (filename && filename.includes("_icon")) {
            // 打印原始文件名，帮助调试
            console.log(`处理图标文件: ${filename}`);

            // 获取图标类型（如ac_icon, router_icon等）
            const iconType = filename.split("_icon")[0] + "_icon";

            // 直接在所有预加载的图标中查找匹配的图标类型
            const matchingIcons = Object.keys(iconModules).filter(path => path.includes(iconType));

            if (matchingIcons.length > 0) {
              // 使用第一个匹配的图标
              url = iconModules[matchingIcons[0]].default;
              console.log(`找到匹配图标: ${matchingIcons[0]} 用于 ${filename}`);
            } else {
              // 如果没找到，尝试使用原始方法
              const baseName = filename.replace(/(-[a-zA-Z0-9]+-)?(\.[^.]+)$/, "$2");
              const iconPath = `/src/assets/images/${baseName}`;

              if (iconModules[iconPath]) {
                url = iconModules[iconPath].default;
                console.log(`使用原始路径找到图标: ${iconPath}`);
              } else {
                console.error(`图标未找到: ${filename}, 尝试路径: ${iconPath}`);
                // 打印所有可用的图标路径，帮助调试
                console.log("可用图标:", Object.keys(iconModules));
                url = "";
              }
            }
          } else if (url.includes("/assets/")) {
            // 其他资源在生产环境下的处理
            const assetsIndex = url.indexOf("/assets/");
            const assetsPath = url.substring(assetsIndex);
            const cleanAssetsPath = assetsPath.startsWith("/") ? assetsPath.substring(1) : assetsPath;
            url = `/assets/${cleanAssetsPath}`; // 确保路径以 /assets/ 开头
          }
        } catch (error) {
          console.error("Error processing URL:", error, url);
        }

        // 打印调试信息 - 对所有URL进行调试
        console.log(
          `URL Debug [${d.data.name || "unknown"}]: Original=${d.data.symbol}, Processed=${url}, Env=${import.meta.env.PROD ? "Prod" : "Dev"}`
        );

        // 测试URL是否可访问
        if (!url.startsWith("data:")) {
          fetch(url, { method: "HEAD" })
            .then(response => {
              if (response.ok) {
                console.log(`URL ${url} is accessible`);
              } else {
                console.error(`URL ${url} returned status ${response.status}`);
              }
            })
            .catch(error => {
              console.error(`URL ${url} is not accessible:`, error.message);
            });
        }

        return url;
      })
      .attr("x", -iconSize / 2)
      .attr("y", -iconSize / 2)
      .attr("width", iconSize)
      .attr("height", iconSize)
      .style("opacity", d => {
        return d.data.extra?.offline === 1 || d.data.extra?.status === 1 || d.data.status === 1 ? 0.6 : 1;
      })
      .attr("filter", d => {
        return d.data.extra?.offline === 1 || d.data.extra?.status === 1 || d.data.status === 1 ? "url(#grayscale)" : null;
      })
      .on("click", handleNodeClick)
      .on("error", function (event, d) {
        d3.select(this).remove();
        d3.select(this.parentNode)
          .append("circle")
          .attr("r", iconSize / 2)
          .attr("fill", d.data.extra?.offline === 1 || d.data.status === 1 ? "#999" : "#ccc")
          .style("opacity", d.data.extra?.offline === 1 || d.data.status === 1 ? 0.6 : 1)
          .on("click", function (event) {
            const parentNode = d3.select(this.parentNode).datum();
            handleNodeClick(event, parentNode);
          });
        if (d.data.extra?.offline === 1 || d.data.status === 1) {
          d3.select(this.parentNode)
            .append("line")
            .attr("x1", -iconSize / 3)
            .attr("y1", -iconSize / 3)
            .attr("x2", iconSize / 3)
            .attr("y2", iconSize / 3)
            .attr("stroke", "#ff0000")
            .attr("stroke-width", 2);
        }
      });

    // 添加上方标签（上联口和下联口）
    node
      .filter(d => {
        const extra = d.data.extra || {};
        return extra.dport || extra.sport;
      })
      .append("g")
      .attr("transform", () => {
        return currentDirection.value === "vertical" ? `translate(0, -${iconSize})` : `translate(-${iconSize}, 0)`;
      })
      .attr("pointer-events", "none")
      .each(function (d) {
        const extra = d.data.extra || {};
        let content = [];
        content.push(`${t("topology.uplink")}: ${extra.sport ? extra.sport : "--"}`);
        content.push(`${t("topology.downlink")}: ${extra.dport ? extra.dport : "--"}`);
        const labelGroup = d3.select(this);
        const lineHeight = 14;
        const text = labelGroup
          .append("text")
          .attr("text-anchor", "middle")
          .attr("font-size", "9px")
          .attr("fill", d => {
            return d.data.extra?.offline === 1 || d.data.extra?.status === 1 || d.data.status === 1
              ? "#999"
              : globalStore.isDark
                ? "#fff"
                : "#333";
          })
          .attr("font-weight", "normal")
          .attr("stroke", globalStore.isDark ? "rgba(0,0,0,0.5)" : "none")
          .attr("stroke-width", globalStore.isDark ? "0.3" : "0")
          .style("opacity", d => {
            return d.data.extra?.offline === 1 || d.data.extra?.status === 1 || d.data.status === 1 ? 0.8 : 1;
          });
        content.forEach((line, i) => {
          text
            .append("tspan")
            .attr("x", 0)
            .attr("dy", i === 0 ? 0 : lineHeight)
            .text(line);
        });
      });

    // 添加下方标签（节点名称）
    node
      .append("text")
      .attr("dy", () => (currentDirection.value === "vertical" ? iconSize * 0.9 : 0))
      .attr("dx", () => (currentDirection.value === "vertical" ? 0 : iconSize * 0.9))
      .attr("text-anchor", "middle")
      .attr("font-size", "9px")
      .attr("fill", d => {
        return d.data.extra?.offline === 1 || d.data.extra?.status === 1 || d.data.status === 1
          ? "#999"
          : globalStore.isDark
            ? "#fff"
            : "#333";
      })
      .attr("stroke", globalStore.isDark ? "rgba(0,0,0,0.5)" : "none")
      .attr("stroke-width", globalStore.isDark ? "0.3" : "0")
      .style("opacity", d => {
        return d.data.extra?.offline === 1 || d.data.extra?.status === 1 || d.data.status === 1 ? 0.8 : 1;
      })
      .text(d => {
        const name = d.data.name;
        return name.includes(".") ? name.split(".").pop() : name;
      })
      .on("click", handleNodeClick);

    // 为离线节点添加标记
    node
      .filter(d => d.data.extra?.offline === 1 || d.data.extra?.status === 1 || d.data.status === 1)
      .append("circle")
      .attr("r", iconSize * 0.13)
      .attr("cx", iconSize * 0.38)
      .attr("cy", -iconSize * 0.38)
      .attr("fill", "#ff0000")
      .attr("stroke", "#ffffff")
      .attr("stroke-width", 1);
  };

  // 组件挂载时添加事件监听
  onMounted(() => {
    document.addEventListener("click", handleDocumentClick);
  });

  // 组件卸载时移除事件监听
  onBeforeUnmount(() => {
    document.removeEventListener("click", handleDocumentClick);
  });

  // 监听数据变化
  watch(
    [() => props.data, () => locale.value, () => globalStore.isDark],
    () => {
      console.log("Data, locale or theme changed, refreshing chart...");
      // 当数据变化时，关闭设备信息弹窗
      showDeviceInfo.value = false;
      selectedDevice.value = {};
      initChart();
    },
    { deep: true, immediate: true }
  );

  // 监听布局方向变化
  watch(
    () => props.direction,
    newDirection => {
      currentDirection.value = newDirection;
      initChart();
    }
  );

  // 监听窗口大小变化
  const handleResize = () => {
    if (svg) {
      // 确保宽度和高度始终为正值
      const width = Math.max(100, props.width || 100);
      const height = Math.max(100, props.height || 100);

      console.log(`窗口大小变化: 宽度=${width}, 高度=${height}`);

      // 更新SVG元素的尺寸
      svg.attr("width", width).attr("height", height).attr("viewBox", [0, 0, width, height]);

      // 重新绘制图表以适应新尺寸
      // 使用setTimeout确保在下一个事件循环中调用，避免多次调用
      setTimeout(() => {
        initChart();
      }, 0);
    }
  };

  watch([() => props.width, () => props.height], handleResize);

  /**
   * 获取端口类型，分为LAN、WAN、RA三大类
   * @param {string} port 端口名称
   * @returns {string} 端口类型
   */
  const getPortType = port => {
    if (!port) return "";

    // 转换为小写进行比较
    const portLower = port.toLowerCase();

    // WAN类型端口
    if (portLower.startsWith("wan")) {
      return "WAN";
    }

    // RA类型端口
    if (portLower === "racli" || /^ra\d+$/i.test(portLower)) {
      return "RA";
    }

    // LAN类型端口，包括lan、ge、port、fe等
    if (portLower.startsWith("lan") || portLower.startsWith("ge") || portLower.startsWith("port") || portLower.startsWith("fe")) {
      return "LAN";
    }

    // 默认为LAN类型
    return "LAN";
  };

  return {
    container,
    currentDirection,
    selectedDevice,
    showDeviceInfo,
    popupX,
    popupY,
    refreshTopology,
    toggleDirection,
    resetZoom,
    closeDeviceInfo,
    initChart
  };
}
