/* ECharts 暗黑模式样式 */
html.dark {
  /* 图表容器 */
  .echarts-container {
    background-color: var(--el-bg-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  /* 图表文字颜色 */
  .echarts-text,
  .echarts-legend-text {
    color: #e0e0e0 !important;
  }

  /* 图表图例样式 */
  .echarts-legend {
    background-color: rgba(30, 30, 30, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    padding: 4px 8px;
  }

  /* 确保SVG文本在暗黑模式下可见 */
  svg text {
    fill: #e0e0e0 !important;
  }

  /* 图表提示框样式 */
  .echarts-tooltip {
    background-color: rgba(30, 30, 30, 0.9) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 4px !important;
    color: #ffffff !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
  }
}
