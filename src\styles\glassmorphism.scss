/* 全局毛玻璃效果样式 */

// 基础毛玻璃效果
.glass-effect {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.18) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
}

// 卡片毛玻璃效果
.glass-card {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.18) !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease !important;

  &:hover {
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15) !important;
    transform: translateY(-5px) !important;
    background: rgba(255, 255, 255, 0.2) !important;
  }
}

// 内容卡片毛玻璃效果
.glass-content-card {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  border: 1px solid rgba(255, 255, 255, 0.18) !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;
  transition: all 0.3s ease !important;

  &:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
    transform: translateY(-3px) !important;
    background: rgba(255, 255, 255, 0.15) !important;
  }
}

// 容器毛玻璃效果
.glass-container {
  position: relative !important;
  overflow: hidden !important;

  &::before {
    content: "" !important;
    position: absolute !important;
    top: -100px !important;
    right: -100px !important;
    width: 300px !important;
    height: 300px !important;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 70%) !important;
    opacity: 0.4 !important;
    z-index: 0 !important;
  }

  &::after {
    content: "" !important;
    position: absolute !important;
    bottom: -100px !important;
    left: -100px !important;
    width: 300px !important;
    height: 300px !important;
    background: radial-gradient(circle, rgba(173, 216, 230, 0.8) 0%, rgba(173, 216, 230, 0) 70%) !important;
    opacity: 0.3 !important;
    z-index: 0 !important;
  }
}

// 背景渐变
.glass-bg {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
}

// 对话框毛玻璃效果
.glass-dialog {
  .el-dialog {
    background: rgba(255, 255, 255, 0.7) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.18) !important;
    border-radius: 16px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
    overflow: hidden !important;

    .el-dialog__header {
      background: rgba(255, 255, 255, 0.1) !important;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
    }

    .el-dialog__body {
      background: rgba(255, 255, 255, 0.05) !important;
    }

    .el-dialog__footer {
      background: rgba(255, 255, 255, 0.1) !important;
      border-top: 1px solid rgba(255, 255, 255, 0.2) !important;
    }
  }
}

// 暗色模式适配
.dark {
  .glass-effect,
  .glass-card,
  .glass-content-card {
    background: rgba(30, 30, 30, 0.3) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
  }

  .glass-card:hover {
    background: rgba(40, 40, 40, 0.4) !important;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.3) !important;
  }

  .glass-content-card:hover {
    background: rgba(40, 40, 40, 0.4) !important;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2) !important;
  }

  .glass-bg {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%) !important;
  }

  .glass-container {
    &::before {
      background: radial-gradient(circle, rgba(70, 130, 180, 0.3) 0%, rgba(70, 130, 180, 0) 70%) !important;
    }

    &::after {
      background: radial-gradient(circle, rgba(255, 100, 100, 0.3) 0%, rgba(255, 100, 100, 0) 70%) !important;
    }
  }

  .glass-dialog .el-dialog {
    background: rgba(30, 30, 30, 0.7) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;

    .el-dialog__header {
      background: rgba(40, 40, 40, 0.5) !important;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    }

    .el-dialog__body {
      background: rgba(30, 30, 30, 0.3) !important;
    }

    .el-dialog__footer {
      background: rgba(40, 40, 40, 0.5) !important;
      border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    }
  }
}
