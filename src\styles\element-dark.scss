/* 自定义 element 暗黑模式 */
html.dark {
  /* 毛玻璃效果 - 暗色模式 */
  .glass-effect {
    background: rgba(30, 30, 30, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .glass-card:hover {
    background: rgba(40, 40, 40, 0.4);
  }

  .glass-container {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);

    &::before {
      background: radial-gradient(circle, rgba(100, 100, 100, 0.5) 0%, rgba(100, 100, 100, 0) 70%);
    }

    &::after {
      background: radial-gradient(circle, rgba(70, 130, 180, 0.3) 0%, rgba(70, 130, 180, 0) 70%);
    }
  }

  /* 图表样式 - 暗色模式 */
  #echarts {
    .echarts-tooltip {
      background: rgba(30, 30, 30, 0.8) !important;
      border: 1px solid rgba(255, 255, 255, 0.1) !important;
      color: #ffffff !important;
    }

    /* 确保图例文字在暗色模式下可见 */
    .echarts-legend-text {
      color: #ffffff !important;
    }
  }

  /* 对话框 */
  .el-dialog {
    background: rgba(30, 30, 30, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);

    .el-dialog__header {
      background: rgba(40, 40, 40, 0.5);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .el-dialog__body {
      background: rgba(30, 30, 30, 0.3);

      /* 确保对话框中的文本可见 */
      color: #e5eaf3;

      /* 对话框中的链接样式 */
      .el-link {
        color: #409eff !important;
        font-weight: 600;
        text-shadow: 0 0 8px rgba(64, 158, 255, 0.5);

        &:hover {
          color: #79bbff !important;
          text-shadow: 0 0 12px rgba(64, 158, 255, 0.8);
        }
      }
    }

    .el-dialog__footer {
      background: rgba(40, 40, 40, 0.5);
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }
  }
  /* wangEditor */
  --w-e-toolbar-color: #eeeeee;
  --w-e-toolbar-bg-color: #141414;
  --w-e-textarea-bg-color: #141414;
  --w-e-textarea-color: #eeeeee;
  --w-e-toolbar-active-bg-color: #464646;
  --w-e-toolbar-border-color: var(--el-border-color-darker);
  .w-e-bar-item button:hover,
  .w-e-menu-tooltip-v5::before {
    color: #eeeeee;
  }

  /* login */
  .login-container {
    background-color: #191919 !important;
    .login-box {
      background-color: rgb(0 0 0 / 80%) !important;
      .login-form {
        box-shadow: rgb(255 255 255 / 12%) 0 2px 10px 2px !important;
        background-color: rgba(30, 30, 30, 0.7) !important;

        .logo-text {
          color: var(--el-text-color-primary) !important;
        }

        /* 登录按钮区域 */
        .login-btn {
          color: #e5eaf3 !important;

          /* 注册和忘记密码链接 */
          .el-link {
            color: #409eff !important;
            font-weight: 600;
            text-shadow: 0 0 8px rgba(64, 158, 255, 0.5);

            &:hover {
              color: #79bbff !important;
              text-shadow: 0 0 12px rgba(64, 158, 255, 0.8);
            }
          }
        }
      }
    }
  }

  /* ECharts 图表组件样式 */
  .echarts-for-react,
  .echarts,
  #echarts,
  .chart-container {
    /* 确保图例文字在暗色模式下清晰可见 */
    .echarts-legend {
      color: #ffffff !important;
    }

    /* 修复图例文字颜色 */
    text {
      fill: #ffffff !important;
      font-weight: bold !important;
    }

    /* 直接针对图例元素 */
    .defs + g {
      g {
        text {
          fill: #ffffff !important;
          font-weight: bold !important;
          font-size: 14px !important;
        }
      }
    }

    /* 针对图例元素 - 更具体的选择器 */
    div[style*="position: absolute"] {
      background-color: rgba(50, 50, 50, 0.8) !important;
      border: 1px solid rgba(255, 255, 255, 0.2) !important;
      border-radius: 4px !important;
      padding: 5px !important;

      span {
        color: #ffffff !important;
        font-weight: bold !important;
        font-size: 14px !important;
      }
    }
  }
}
